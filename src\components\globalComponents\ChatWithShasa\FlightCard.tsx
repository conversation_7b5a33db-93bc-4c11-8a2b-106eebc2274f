'use client';

import React, { useState } from 'react';
import { Card, CardBody, Button, Chip, Divider } from '@heroui/react';
import { FaPlane, FaCalendarAlt } from 'react-icons/fa';
import { IoIosArrowUp, IoIosArrowDown } from 'react-icons/io';
import { MdLuggage, MdAirlineSeatReclineNormal } from 'react-icons/md';

interface FlightData {
  id: string;
  airline: {
    name: string;
    logo: string;
    code: string;
  };
  departure: {
    airport: string;
    city: string;
    time: string;
    date: string;
    terminal?: string;
  };
  arrival: {
    airport: string;
    city: string;
    time: string;
    date: string;
    terminal?: string;
  };
  duration: string;
  stops: number;
  stopDetails?: string;
  aircraft: string;
  class: string;
  price: {
    currency: string;
    amount: string;
  };
  baggage: {
    cabin: string;
    checked: string;
  };
  amenities: string[];
}

interface FlightCardProps {
  flight: FlightData;
  onBookClick?: () => void;
  onSelectClick?: () => void;
}

const FlightCard: React.FC<FlightCardProps> = ({
  flight,
  onBookClick,
  onSelectClick,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDuration = (duration: string) => {
    return duration.replace('h', 'h ').replace('m', 'm');
  };

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-lg hover:shadow-xl transition-all duration-300 border-none">
      <CardBody className="p-0">
        {/* Header - Always Visible */}
        <div
          className="cursor-pointer"
          onClick={() => setIsExpanded(!isExpanded)}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              setIsExpanded(!isExpanded);
            }
          }}
          role="button"
          tabIndex={0}
        >
          <div className="p-4 bg-white">
            <div className="flex items-center justify-between">
              {/* Airline Info */}
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 border border-gray-200 rounded-lg p-2 flex items-center justify-center">
                  <img
                    src={flight.airline.logo}
                    alt={flight.airline.name}
                    className="w-full h-full object-contain"
                  />
                </div>
                <div>
                  <p className="font-semibold text-gray-800">
                    {flight.airline.name}
                  </p>
                  <p className="text-sm text-gray-500">{flight.airline.code}</p>
                </div>
              </div>

              {/* Price and Toggle */}
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <p className="text-xl font-bold text-blue-600">
                    {flight.price.currency}
                    {flight.price.amount}
                  </p>
                  <p className="text-sm text-gray-500">{flight.class}</p>
                </div>
                {isExpanded ? (
                  <IoIosArrowUp size={20} className="text-gray-400" />
                ) : (
                  <IoIosArrowDown size={20} className="text-gray-400" />
                )}
              </div>
            </div>

            {/* Flight Route */}
            <div className="mt-4 flex items-center justify-between">
              {/* Departure */}
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-800">
                  {flight.departure.time}
                </p>
                <p className="text-sm text-gray-600">
                  {flight.departure.airport}
                </p>
                <p className="text-xs text-gray-500">{flight.departure.city}</p>
              </div>

              {/* Flight Path */}
              <div className="flex-1 mx-6 flex flex-col items-center">
                <div className="relative w-full flex items-center justify-center">
                  <div className="w-full border-t-2 border-gray-300" />
                  <FaPlane
                    className="absolute text-blue-500 bg-white px-2"
                    size={20}
                  />
                </div>
                <div className="mt-2 text-center">
                  <p className="text-sm font-medium text-gray-600">
                    {formatDuration(flight.duration)}
                  </p>
                  <p className="text-xs text-gray-500">
                    {flight.stops === 0
                      ? 'Direct'
                      : `${flight.stops} stop${flight.stops > 1 ? 's' : ''}`}
                  </p>
                </div>
              </div>

              {/* Arrival */}
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-800">
                  {flight.arrival.time}
                </p>
                <p className="text-sm text-gray-600">
                  {flight.arrival.airport}
                </p>
                <p className="text-xs text-gray-500">{flight.arrival.city}</p>
              </div>
            </div>

            {/* Quick Info */}
            <div className="mt-3 flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <FaCalendarAlt size={12} />
                  <span>{flight.departure.date}</span>
                </div>
                <div className="flex items-center gap-1">
                  <MdAirlineSeatReclineNormal size={14} />
                  <span>{flight.aircraft}</span>
                </div>
              </div>
              {flight.stops > 0 && flight.stopDetails && (
                <span className="text-xs text-orange-600">
                  {flight.stopDetails}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Expandable Content */}
        <div
          className={`transition-all duration-500 ease-in-out overflow-hidden ${
            isExpanded ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
          }`}
        >
          <Divider />
          <div className="p-4 bg-blue-400 space-y-4">
            {/* Flight Details */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">
                  Departure Details
                </h4>
                <div className="space-y-1 text-sm">
                  <p>
                    <span className="text-gray-600">Terminal:</span>{' '}
                    {flight.departure.terminal || 'TBD'}
                  </p>
                  <p>
                    <span className="text-gray-600">Date:</span>{' '}
                    {flight.departure.date}
                  </p>
                  <p>
                    <span className="text-gray-600">Time:</span>{' '}
                    {flight.departure.time}
                  </p>
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">
                  Arrival Details
                </h4>
                <div className="space-y-1 text-sm">
                  <p>
                    <span className="text-gray-600">Terminal:</span>{' '}
                    {flight.arrival.terminal || 'TBD'}
                  </p>
                  <p>
                    <span className="text-gray-600">Date:</span>{' '}
                    {flight.arrival.date}
                  </p>
                  <p>
                    <span className="text-gray-600">Time:</span>{' '}
                    {flight.arrival.time}
                  </p>
                </div>
              </div>
            </div>

            {/* Baggage Info */}
            <div>
              <h4 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                <MdLuggage size={16} />
                Baggage Allowance
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p>
                    <span className="text-gray-600">Cabin:</span>{' '}
                    {flight.baggage.cabin}
                  </p>
                </div>
                <div>
                  <p>
                    <span className="text-gray-600">Checked:</span>{' '}
                    {flight.baggage.checked}
                  </p>
                </div>
              </div>
            </div>

            {/* Amenities */}
            {flight.amenities.length > 0 && (
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">Amenities</h4>
                <div className="flex flex-wrap gap-2">
                  {flight.amenities.map((amenity, index) => (
                    <Chip
                      key={index}
                      size="sm"
                      variant="flat"
                      className="bg-green-100 text-green-800"
                    >
                      {amenity}
                    </Chip>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2 pt-2">
              <Button
                size="sm"
                color="primary"
                className="flex-1"
                onPress={onBookClick}
              >
                Book Flight
              </Button>
              <Button
                size="sm"
                variant="bordered"
                className="flex-1"
                onPress={onSelectClick}
              >
                Select Flight
              </Button>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default FlightCard;
